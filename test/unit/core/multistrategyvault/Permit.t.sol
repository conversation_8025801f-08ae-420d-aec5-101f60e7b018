// SPDX-License-Identifier: UNLICENSED
pragma solidity >=0.8.25;

import "forge-std/console.sol";
import { Setup } from "test/unit/zodiac-core/vaults/Setup.sol";
import { IERC20Permit } from "src/utils/vendor/shamirlabs/IERC20Permit.sol";
import { MultistrategyVault } from "src/core/MultistrategyVault.sol";
import { MultistrategyVaultFactory } from "src/factories/MultistrategyVaultFactory.sol";
import { IMultistrategyVault } from "src/core/interfaces/IMultistrategyVault.sol";

contract PermitTest is Setup {
    uint256 constant AMOUNT = 10 ** 18;
    uint256 constant PRIVATE_KEY = 0xabcd; // Known private key for tests

    MultistrategyVault public vault;
    MultistrategyVault public vaultImplementation;
    address public bunny;
    MultistrategyVaultFactory public vaultFactory;

    function setUp() public override {
        super.setUp();

        // Setup bunny address (similar to Python test's bunny)
        bunny = address(0x1234);

        // Deploy vault implementation
        vaultImplementation = new MultistrategyVault();

        vaultFactory = new MultistrategyVaultFactory("Test Vault", address(vaultImplementation), management);

        // Create a vault using the asset from Setup
        vault = MultistrategyVault(vaultFactory.deployNewVault(address(asset), "Test Vault", "tVAULT", bunny, 10 days));

        // Label addresses for easier debugging
        vm.label(bunny, "bunny");
        vm.label(address(vault), "vault");
    }

    function testPermit() public {
        address owner = vm.addr(PRIVATE_KEY);
        uint256 deadline = block.timestamp + 3600;

        assertEq(vault.allowance(owner, bunny), 0);

        bytes32 digest = _getPermitDigest(address(vault), owner, bunny, AMOUNT, vault.nonces(owner), deadline);

        (uint8 v, bytes32 r, bytes32 s) = vm.sign(PRIVATE_KEY, digest);

        vm.prank(bunny);
        vault.permit(owner, bunny, AMOUNT, deadline, v, r, s);

        assertEq(vault.allowance(owner, bunny), AMOUNT);
    }

    function testPermitWithUsedPermit() public {
        address owner = vm.addr(PRIVATE_KEY);
        uint256 deadline = block.timestamp + 3600;

        bytes32 digest = _getPermitDigest(address(vault), owner, bunny, AMOUNT, vault.nonces(owner), deadline);

        (uint8 v, bytes32 r, bytes32 s) = vm.sign(PRIVATE_KEY, digest);

        vm.prank(bunny);
        vault.permit(owner, bunny, AMOUNT, deadline, v, r, s);

        vm.expectRevert();
        vm.prank(bunny);
        vault.permit(owner, bunny, AMOUNT, deadline, v, r, s);
    }

    function testPermitWithWrongSignature() public {
        address owner = vm.addr(PRIVATE_KEY);
        uint256 deadline = block.timestamp + 3600;

        // Generate signature for max uint value instead of AMOUNT
        bytes32 digest = _getPermitDigest(
            address(vault),
            owner,
            bunny,
            type(uint256).max,
            vault.nonces(owner),
            deadline
        );

        (uint8 v, bytes32 r, bytes32 s) = vm.sign(PRIVATE_KEY, digest);

        // Try to use the signature for AMOUNT instead
        vm.expectRevert(IMultistrategyVault.InvalidSignature.selector);
        vm.prank(bunny);
        vault.permit(owner, bunny, AMOUNT, deadline, v, r, s);
    }

    function testPermitWithExpiredDeadline() public {
        // Set block timestamp to 1000
        vm.warp(1000);

        address owner = vm.addr(PRIVATE_KEY);
        uint256 deadline = block.timestamp - 600; // Expired deadline

        bytes32 digest = _getPermitDigest(address(vault), owner, bunny, AMOUNT, vault.nonces(owner), deadline);

        (uint8 v, bytes32 r, bytes32 s) = vm.sign(PRIVATE_KEY, digest);

        vm.expectRevert(IMultistrategyVault.PermitExpired.selector);
        vm.prank(bunny);
        vault.permit(owner, bunny, AMOUNT, deadline, v, r, s);
    }

    function testPermitWithBadOwner() public {
        address owner = vm.addr(PRIVATE_KEY);
        uint256 deadline = block.timestamp + 3600;

        bytes32 digest = _getPermitDigest(address(vault), owner, bunny, AMOUNT, vault.nonces(owner), deadline);

        (uint8 v, bytes32 r, bytes32 s) = vm.sign(PRIVATE_KEY, digest);

        vm.expectRevert(IMultistrategyVault.InvalidOwner.selector);
        vm.prank(bunny);
        vault.permit(
            address(0), // Use zero address instead of the real owner
            bunny,
            AMOUNT,
            deadline,
            v,
            r,
            s
        );
    }

    // Helper function to generate permit digest according to EIP-712
    function _getPermitDigest(
        address token,
        address owner,
        address spender,
        uint256 value,
        uint256 nonce,
        uint256 deadline
    ) internal view returns (bytes32) {
        bytes32 PERMIT_TYPEHASH = keccak256(
            "Permit(address owner,address spender,uint256 value,uint256 nonce,uint256 deadline)"
        );

        bytes32 structHash = keccak256(abi.encode(PERMIT_TYPEHASH, owner, spender, value, nonce, deadline));

        return keccak256(abi.encodePacked("\x19\x01", IMultistrategyVault(token).DOMAIN_SEPARATOR(), structHash));
    }

    function test_POC_EIP712DomainSeparatorMismatch() public {
        // Step 1: Verify initial state - vault has a name
        string memory initialName = vault.name();
        assertEq(initialName, "Test Vault", "Initial vault name should be 'Test Vault'");

        // Step 2: Generate domains using CORRECT vs WRONG name
        bytes32 correctDomain = _buildCorrectDomainSeparator(address(vault), initialName);
        bytes32 wrongDomain = vault.DOMAIN_SEPARATOR(); // Uses hardcoded "Octant Vault"

        // Step 3: Verify the domains are different (this is the bug)
        assertTrue(correctDomain != wrongDomain, "BUG: Domain separators should be different");

        // Step 4: Change the vault name to make the mismatch more obvious
        vm.prank(bunny); // bunny is the role manager
        vault.setName("My Custom Vault");
        assertEq(vault.name(), "My Custom Vault", "Vault name should be updated");

        // Step 5: The domain separator still uses hardcoded "Octant Vault"
        bytes32 domainAfterNameChange = vault.DOMAIN_SEPARATOR();
        assertEq(domainAfterNameChange, wrongDomain, "BUG: Domain separator unchanged despite name change");

        // Step 6: Build what the domain separator SHOULD be with the new name
        bytes32 expectedDomain = _buildCorrectDomainSeparator(address(vault), "My Custom Vault");
        assertTrue(domainAfterNameChange != expectedDomain, "BUG: Domain doesn't reflect current name");

        // Step 7: Demonstrate permit failure with off-chain signature
        address owner = vm.addr(PRIVATE_KEY);
        uint256 nonce = vault.nonces(owner);
        uint256 deadline = block.timestamp + 3600;

        // Most EIP-2612 tooling would generate signatures using the current name()
        bytes32 toolingDigest = _buildPermitDigest(expectedDomain, owner, bunny, AMOUNT, nonce, deadline);
        (uint8 v, bytes32 r, bytes32 s) = vm.sign(PRIVATE_KEY, toolingDigest);

        // This permit call will fail because the signature was generated with the correct
        // domain (using current name) but the contract validates with hardcoded domain
        vm.expectRevert(); // Should revert with invalid signature
        vault.permit(owner, bunny, AMOUNT, deadline, v, r, s);

        // Step 8: Show that permit works with the hardcoded domain signature
        bytes32 hardcodedDigest = _buildPermitDigest(wrongDomain, owner, bunny, AMOUNT, nonce, deadline);
        (uint8 v2, bytes32 r2, bytes32 s2) = vm.sign(PRIVATE_KEY, hardcodedDigest);

        // This works because both signature and validation use hardcoded "Octant Vault"
        vault.permit(owner, bunny, AMOUNT, deadline, v2, r2, s2);
        assertEq(vault.allowance(owner, bunny), AMOUNT, "Permit should work with hardcoded domain");

    }

    /**
     * @dev Helper to build correct domain separator using actual vault name
     */
    function _buildCorrectDomainSeparator(address vaultAddr, string memory vaultName) internal view returns (bytes32) {
        bytes32 DOMAIN_TYPE_HASH = keccak256("EIP712Domain(string name,string version,uint256 chainId,address verifyingContract)");
        return keccak256(abi.encode(
            DOMAIN_TYPE_HASH,
            keccak256(bytes(vaultName)), // Use actual vault name
            keccak256(bytes("3.0.4")),   // API_VERSION
            block.chainid,
            vaultAddr
        ));
    }

    /**
     * @dev Helper to build permit digest
     */
    function _buildPermitDigest(
        bytes32 domainSeparator,
        address owner,
        address spender,
        uint256 amount,
        uint256 nonce,
        uint256 deadline
    ) internal pure returns (bytes32) {
        bytes32 PERMIT_TYPE_HASH = keccak256("Permit(address owner,address spender,uint256 value,uint256 nonce,uint256 deadline)");
        bytes32 structHash = keccak256(abi.encode(PERMIT_TYPE_HASH, owner, spender, amount, nonce, deadline));
        return keccak256(abi.encodePacked("\x19\x01", domainSeparator, structHash));
    }
}
