// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "forge-std/Test.sol";
import { MultistrategyVault } from "src/core/MultistrategyVault.sol";
import { MultistrategyVaultFactory } from "src/factories/MultistrategyVaultFactory.sol";
import { IMultistrategyVault } from "src/core/interfaces/IMultistrategyVault.sol";
import { MockERC20 } from "test/mocks/MockERC20.sol";
import { MockLossyStrategy } from "test/mocks/core/MockLossyStrategy.sol";

/// @title Debt Reduction DoS POC
/// @notice Demonstrates how minimal unrealized losses can DoS debt reduction while allowing withdrawals
/// @dev Shows the asymmetry between debt reduction (hard revert) and withdrawals (graceful degradation)
contract DebtReductionDoSPOCTest is Test {
    MultistrategyVault vault;
    MultistrategyVaultFactory vaultFactory;
    MockERC20 asset;
    MockLossyStrategy strategy;

    address gov = address(0x1);
    address user = address(0x2);
    address keeper = address(0x3);

    uint256 constant INITIAL_DEPOSIT = 1000e18;
    uint256 constant TINY_LOSS = 1; // 1 wei loss to trigger DoS

    function setUp() public {
        // Deploy infrastructure
        asset = new MockERC20(18);
        MultistrategyVault vaultImpl = new MultistrategyVault();
        vaultFactory = new MultistrategyVaultFactory("Test Factory", address(vaultImpl), gov);

        // Create vault
        vm.startPrank(gov);
        vault = MultistrategyVault(
            vaultFactory.deployNewVault(address(asset), "Test Vault", "vTST", gov, 0)
        );
        
        // Set up roles
        vault.addRole(gov, IMultistrategyVault.Roles.ADD_STRATEGY_MANAGER);
        vault.addRole(gov, IMultistrategyVault.Roles.DEBT_MANAGER);
        vault.addRole(gov, IMultistrategyVault.Roles.MAX_DEBT_MANAGER);
        vault.addRole(gov, IMultistrategyVault.Roles.DEPOSIT_LIMIT_MANAGER);
        vault.addRole(keeper, IMultistrategyVault.Roles.DEBT_MANAGER);
        vault.setDepositLimit(type(uint256).max, true);
        vm.stopPrank();

        // Create and add strategy
        strategy = new MockLossyStrategy(address(asset), address(vault));
        vm.startPrank(gov);
        vault.addStrategy(address(strategy), true);
        vault.updateMaxDebtForStrategy(address(strategy), type(uint256).max);
        vm.stopPrank();

        // Fund accounts
        asset.mint(user, INITIAL_DEPOSIT);
        asset.mint(keeper, INITIAL_DEPOSIT);
    }

    /// @notice POC demonstrating debt reduction DoS with minimal unrealized losses
    /// @dev Shows how 1 wei loss blocks debt reduction but allows withdrawals
    function test_POC_DebtReductionDoS() public {
        // === SETUP: User deposits and funds are allocated to strategy ===
        vm.startPrank(user);
        asset.approve(address(vault), INITIAL_DEPOSIT);
        vault.deposit(INITIAL_DEPOSIT, user);
        vm.stopPrank();

        // Allocate all funds to strategy
        vm.prank(gov);
        vault.updateDebt(address(strategy), INITIAL_DEPOSIT, 0);

        // Verify initial state
        assertEq(vault.totalDebt(), INITIAL_DEPOSIT, "Initial total debt");
        assertEq(vault.strategies(address(strategy)).currentDebt, INITIAL_DEPOSIT, "Strategy debt");
        assertEq(strategy.totalAssets(), INITIAL_DEPOSIT, "Strategy assets");

        // === ATTACK: Create minimal unrealized loss (1 wei) ===
        // Simulate tiny loss that could occur from rounding, fees, or oracle noise
        strategy.setLoss(TINY_LOSS);

        // Verify the strategy now has unrealized losses
        uint256 strategyAssets = strategy.totalAssets();
        uint256 currentDebt = vault.strategies(address(strategy)).currentDebt;
        assertEq(strategyAssets, INITIAL_DEPOSIT - TINY_LOSS, "Strategy has tiny loss");
        assertLt(strategyAssets, currentDebt, "Strategy assets < debt (unrealized loss)");

        // Check that assessShareOfUnrealisedLosses detects the loss
        uint256 unrealizedLoss = vault.assessShareOfUnrealisedLosses(address(strategy), INITIAL_DEPOSIT / 2);
        assertGt(unrealizedLoss, 0, "Unrealized loss detected");

        // === VULNERABILITY: Debt reduction is completely blocked ===
        uint256 targetDebt = INITIAL_DEPOSIT / 2; // Try to reduce debt by 50%
        
        vm.prank(keeper);
        vm.expectRevert(IMultistrategyVault.StrategyHasUnrealisedLosses.selector);
        vault.updateDebt(address(strategy), targetDebt, 0);

        // Even with max loss tolerance, still blocked due to hard revert
        vm.prank(keeper);
        vm.expectRevert(IMultistrategyVault.StrategyHasUnrealisedLosses.selector);
        vault.updateDebt(address(strategy), targetDebt, 10000); // 100% max loss

        // === CONTRAST: User withdrawals work fine despite the same loss ===
        uint256 withdrawAmount = INITIAL_DEPOSIT / 4; // 25% withdrawal
        
        vm.startPrank(user);
        uint256 sharesToRedeem = vault.convertToShares(withdrawAmount);
        
        // Withdrawal succeeds by realizing the proportional loss
        address[] memory strategies = new address[](0); // Use default queue
        uint256 assetsReceived = vault.redeem(sharesToRedeem, user, user, 10000, strategies);
        vm.stopPrank();

        // User receives slightly less due to realized loss, but withdrawal succeeds
        assertLt(assetsReceived, withdrawAmount, "User takes proportional loss");
        assertGt(assetsReceived, withdrawAmount - 100, "Loss is minimal"); // Loss should be tiny

        // === IMPORTANT: The withdrawal realized the loss, so debt reduction now works ===
        // This shows the asymmetry - withdrawals can proceed by realizing losses,
        // but debt reduction was completely blocked until the loss was realized

        // Now debt reduction works because the loss was realized during withdrawal
        vm.prank(keeper);
        uint256 newDebt = vault.updateDebt(address(strategy), targetDebt, 0);
        assertEq(newDebt, targetDebt, "Debt reduction now works after loss realization");

        // === DEMONSTRATE PERSISTENT DoS: Create another tiny loss ===
        strategy.setLoss(1); // Create another 1 wei loss

        // Verify the strategy has unrealized losses again
        uint256 newUnrealizedLoss = vault.assessShareOfUnrealisedLosses(address(strategy), 100e18);
        assertGt(newUnrealizedLoss, 0, "New unrealized loss detected");

        // Debt reduction is blocked again
        vm.prank(keeper);
        vm.expectRevert(IMultistrategyVault.StrategyHasUnrealisedLosses.selector);
        vault.updateDebt(address(strategy), 100e18, 0);
    }
}
