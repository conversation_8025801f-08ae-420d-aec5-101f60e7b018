// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "forge-std/Test.sol";
import { MultistrategyVault } from "src/core/MultistrategyVault.sol";
import { MultistrategyVaultFactory } from "src/factories/MultistrategyVaultFactory.sol";
import { IMultistrategyVault } from "src/core/interfaces/IMultistrategyVault.sol";
import { MockERC20 } from "test/mocks/MockERC20.sol";
import { MockYieldStrategy } from "test/mocks/zodiac-core/MockYieldStrategy.sol";

/// @title Book Value Mispricing POC
/// @notice Demonstrates vulnerability where deposits are mispriced when unbooked assets exist
/// @dev Shows how attackers can exploit the gap between live wallet balance and book value
contract BookValueMispricingPOCTest is Test {
    MultistrategyVault vault;
    MultistrategyVaultFactory vaultFactory;
    MockERC20 asset;

    address gov = address(0x1);
    address attacker = address(0x2);
    address victim = address(0x3);

    uint256 constant INITIAL_DEPOSIT = 1000e18;
    uint256 constant AIRDROP_AMOUNT = 500e18; // Unbooked gain

    function setUp() public {
        // Deploy infrastructure
        asset = new MockERC20(18);
        MultistrategyVault vaultImpl = new MultistrategyVault();
        vaultFactory = new MultistrategyVaultFactory("Test Factory", address(vaultImpl), gov);

        // Create vault with 0 profit unlock time to disable profit locking
        vm.startPrank(gov);
        vault = MultistrategyVault(
            vaultFactory.deployNewVault(address(asset), "Test Vault", "vTST", gov, 0)
        );
        vault.addRole(gov, IMultistrategyVault.Roles.DEPOSIT_LIMIT_MANAGER);
        vault.addRole(gov, IMultistrategyVault.Roles.REPORTING_MANAGER);
        vault.addRole(gov, IMultistrategyVault.Roles.ADD_STRATEGY_MANAGER);
        vault.addRole(gov, IMultistrategyVault.Roles.MAX_DEBT_MANAGER);
        vault.addRole(gov, IMultistrategyVault.Roles.DEBT_MANAGER);
        vault.setDepositLimit(type(uint256).max, true);
        vm.stopPrank();

        // Fund accounts
        asset.mint(attacker, 10000e18);
        asset.mint(victim, 10000e18);
    }

    /// @notice POC demonstrating strategy donation attack
    /// @dev Shows how attacker can donate to strategy then exploit stale vault book value
    function test_POC_BookValueMispricing() public {
        // === SETUP: Create strategy and initial vault state ===
        MockYieldStrategy strategy = new MockYieldStrategy(address(asset), address(vault));

        // Add strategy to vault
        vm.startPrank(gov);
        vault.addStrategy(address(strategy), true);
        vault.updateMaxDebtForStrategy(address(strategy), type(uint256).max);
        vm.stopPrank();

        // Initial victim deposit
        vm.startPrank(victim);
        asset.approve(address(vault), INITIAL_DEPOSIT);
        vault.deposit(INITIAL_DEPOSIT, victim);
        vm.stopPrank();

        // Allocate funds to strategy
        vm.prank(gov);
        vault.updateDebt(address(strategy), INITIAL_DEPOSIT, 0);

        // Verify initial state
        assertEq(vault.totalAssets(), INITIAL_DEPOSIT, "Initial total assets");
        assertEq(vault.totalDebt(), INITIAL_DEPOSIT, "Initial total debt");
        assertEq(vault.balanceOf(victim), INITIAL_DEPOSIT, "Victim shares");
        assertEq(vault.pricePerShare(), 1e18, "Initial PPS should be 1:1");

        // === ATTACK STEP 1: Donate to strategy by minting strategy shares to vault ===
        uint256 donationAmount = AIRDROP_AMOUNT;

        // Attacker directly calls strategy.deposit() with vault as receiver
        // This mints strategy shares to the vault without updating vault's book debt
        vm.startPrank(attacker);
        asset.approve(address(strategy), donationAmount);
        strategy.deposit(donationAmount, address(vault));
        vm.stopPrank();

        // Verify donation state: vault owns more strategy shares but book debt unchanged
        uint256 vaultStrategyShares = strategy.balanceOf(address(vault));
        uint256 strategyValue = strategy.convertToAssets(vaultStrategyShares);
        uint256 vaultBookDebt = vault.strategies(address(strategy)).currentDebt;

        assertEq(vaultStrategyShares, INITIAL_DEPOSIT + donationAmount, "Vault owns more strategy shares");
        assertEq(strategyValue, INITIAL_DEPOSIT + donationAmount, "Strategy position worth more");
        assertEq(vaultBookDebt, INITIAL_DEPOSIT, "Vault book debt unchanged (stale)");
        assertEq(vault.totalAssets(), INITIAL_DEPOSIT, "Vault totalAssets still stale");

        // === ATTACK STEP 2: Frontrun processReport with vault deposit ===
        uint256 attackDeposit = INITIAL_DEPOSIT;

        // Record state before attack deposit
        uint256 totalSupplyBefore = vault.totalSupply();
        uint256 totalAssetsBefore = vault.totalAssets(); // Still stale!

        // Attacker deposits into vault using stale book value
        vm.startPrank(attacker);
        asset.approve(address(vault), attackDeposit);
        uint256 attackerShares = vault.deposit(attackDeposit, attacker);
        vm.stopPrank();

        // Verify mispricing: shares calculated with stale denominator
        uint256 expectedShares = (attackDeposit * totalSupplyBefore) / totalAssetsBefore;
        assertEq(attackerShares, expectedShares, "Attacker gets shares based on stale book value");

        // === ATTACK STEP 3: Process report to realize the donation as profit ===
        vm.prank(gov);
        (uint256 gain, uint256 loss) = vault.processReport(address(strategy));

        // Verify the donation is booked as gain
        assertEq(gain, donationAmount, "Donation recognized as gain");
        assertEq(loss, 0, "No loss");

        // === ANALYSIS: Check if the attack worked ===
        uint256 ppsAfter = vault.pricePerShare();
        uint256 attackerValue = (attackerShares * ppsAfter) / 1e18;
        uint256 victimValue = (vault.balanceOf(victim) * ppsAfter) / 1e18;

        // Calculate what fair distribution should be
        uint256 totalValue = INITIAL_DEPOSIT + attackDeposit + donationAmount;
        uint256 victimFairShare = (INITIAL_DEPOSIT * totalValue) / (INITIAL_DEPOSIT + attackDeposit);
        uint256 attackerFairShare = (attackDeposit * totalValue) / (INITIAL_DEPOSIT + attackDeposit);

        // RESULT: The MultistrategyVault correctly handles this attack
        // Even with profitMaxUnlockTime = 0, the vault distributes profits fairly
        assertEq(attackerValue, attackerFairShare, "Vault correctly gives attacker fair share");
        assertEq(victimValue, victimFairShare, "Vault correctly gives victim fair share");

        // The attack fails because the vault's profit distribution mechanism
        // ensures fair allocation regardless of when deposits occur relative to reports
        assertTrue(attackerValue >= attackDeposit, "Attacker gets at least their deposit back");
        assertTrue(victimValue >= INITIAL_DEPOSIT, "Victim gets at least their deposit back");
    }
}
