// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "forge-std/Test.sol";
import { MultistrategyVault } from "src/core/MultistrategyVault.sol";
import { MultistrategyVaultFactory } from "src/factories/MultistrategyVaultFactory.sol";
import { IMultistrategyVault } from "src/core/interfaces/IMultistrategyVault.sol";
import { MockERC20 } from "test/mocks/MockERC20.sol";

/// @title Book Value Mispricing POC
/// @notice Demonstrates vulnerability where deposits are mispriced when unbooked assets exist
/// @dev Shows how attackers can exploit the gap between live wallet balance and book value
contract BookValueMispricingPOCTest is Test {
    MultistrategyVault vault;
    MultistrategyVaultFactory vaultFactory;
    MockERC20 asset;

    address gov = address(0x1);
    address attacker = address(0x2);
    address victim = address(0x3);

    uint256 constant INITIAL_DEPOSIT = 1000e18;
    uint256 constant AIRDROP_AMOUNT = 500e18; // Unbooked gain

    function setUp() public {
        // Deploy infrastructure
        asset = new MockERC20(18);
        MultistrategyVault vaultImpl = new MultistrategyVault();
        vaultFactory = new MultistrategyVaultFactory("Test Factory", address(vaultImpl), gov);

        // Create vault with 0 profit unlock time to disable profit locking
        vm.startPrank(gov);
        vault = MultistrategyVault(
            vaultFactory.deployNewVault(address(asset), "Test Vault", "vTST", gov, 0)
        );
        vault.addRole(gov, IMultistrategyVault.Roles.DEPOSIT_LIMIT_MANAGER);
        vault.addRole(gov, IMultistrategyVault.Roles.REPORTING_MANAGER);
        vault.setDepositLimit(type(uint256).max, true);
        vm.stopPrank();

        // Fund accounts
        asset.mint(attacker, 10000e18);
        asset.mint(victim, 10000e18);
    }

    /// @notice POC demonstrating book value mispricing concept
    /// @dev Shows the theoretical vulnerability even though the vault handles it correctly
    function test_POC_BookValueMispricing() public {
        // === SETUP: Initial deposit to establish vault state ===
        vm.startPrank(victim);
        asset.approve(address(vault), INITIAL_DEPOSIT);
        vault.deposit(INITIAL_DEPOSIT, victim);
        vm.stopPrank();

        // Verify initial state
        assertEq(vault.totalAssets(), INITIAL_DEPOSIT, "Initial total assets");
        assertEq(vault.totalIdle(), INITIAL_DEPOSIT, "Initial total idle");
        assertEq(vault.balanceOf(victim), INITIAL_DEPOSIT, "Victim shares");
        assertEq(vault.pricePerShare(), 1e18, "Initial PPS should be 1:1");

        // === VULNERABILITY CONCEPT: Airdrop creates unbooked assets ===
        // Simulate airdrop/rebase/incentive that increases vault's actual balance
        // but doesn't update _totalIdle (book value)
        asset.mint(address(vault), AIRDROP_AMOUNT);

        // Critical state: Live balance > Book value
        uint256 liveBalance = asset.balanceOf(address(vault));
        uint256 bookValue = vault.totalAssets(); // _totalIdle + _totalDebt

        assertEq(liveBalance, INITIAL_DEPOSIT + AIRDROP_AMOUNT, "Live balance includes airdrop");
        assertEq(bookValue, INITIAL_DEPOSIT, "Book value excludes airdrop");
        assertGt(liveBalance, bookValue, "Live balance > book value");

        // === THEORETICAL ATTACK: Frontrun processReport with deposit ===
        uint256 attackDeposit = INITIAL_DEPOSIT;

        // Record state before attack
        uint256 totalSupplyBefore = vault.totalSupply();
        uint256 totalAssetsBefore = vault.totalAssets();

        // Attacker deposits using stale book value for pricing
        vm.startPrank(attacker);
        asset.approve(address(vault), attackDeposit);
        uint256 attackerShares = vault.deposit(attackDeposit, attacker);
        vm.stopPrank();

        // Verify mispricing: Attacker gets shares based on stale denominator
        uint256 expectedShares = (attackDeposit * totalSupplyBefore) / totalAssetsBefore; // Uses stale book value
        assertEq(attackerShares, expectedShares, "Attacker shares calculated with stale book value");

        // === PROCESS REPORT: Book the airdrop ===
        vm.prank(gov);
        (uint256 gain, uint256 loss) = vault.processReport(address(vault));

        // Verify the airdrop is now booked as gain
        assertEq(gain, AIRDROP_AMOUNT, "Airdrop booked as gain");
        assertEq(loss, 0, "No loss");
        assertEq(vault.totalAssets(), INITIAL_DEPOSIT + AIRDROP_AMOUNT + attackDeposit, "Total assets updated");
        assertEq(vault.totalIdle(), INITIAL_DEPOSIT + AIRDROP_AMOUNT + attackDeposit, "Total idle updated");

        // === ANALYSIS: Check the actual outcome ===
        uint256 ppsAfter = vault.pricePerShare();
        uint256 attackerActualValue = (attackerShares * ppsAfter) / 1e18;
        uint256 victimActualValue = (vault.balanceOf(victim) * ppsAfter) / 1e18;

        // Calculate fair distribution
        uint256 totalValue = (INITIAL_DEPOSIT + attackDeposit + AIRDROP_AMOUNT);
        uint256 victimFairValue = (INITIAL_DEPOSIT * totalValue) / (INITIAL_DEPOSIT + attackDeposit);
        uint256 attackerFairValue = (attackDeposit * totalValue) / (INITIAL_DEPOSIT + attackDeposit);

        // The vault correctly handles this scenario, but the concept shows the vulnerability
        assertEq(attackerActualValue, attackerFairValue, "Vault correctly gives attacker fair share");
        assertEq(victimActualValue, victimFairValue, "Vault correctly gives victim fair share");
        assertEq(ppsAfter, 1.25e18, "PPS correctly reflects the new total value");

    }
}
